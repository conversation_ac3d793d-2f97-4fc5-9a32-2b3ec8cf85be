# Service Restart Task Usage Examples

## Overview
The `task-restart-services.yml` provides a robust and idempotent way to restart Docker Compose services across multiple hosts. It supports both group-based and individual service restart operations.

## Configuration Files
- **Service Configuration**: `extra-vars/services.restart.yml`
- **Task Implementation**: `roles/01-common/tasks/task-restart-services.yml`

## Usage Examples

### 1. Restart Services by Group

```yaml
# Restart all services in the "recognize" group (识别端)
- include_tasks: roles/01-common/tasks/task-restart-services.yml
  vars:
    restart_groups:
      - "recognize"

# Restart services in multiple groups
- include_tasks: roles/01-common/tasks/task-restart-services.yml
  vars:
    restart_groups:
      - "recognize"
      - "api"
      - "bureau"
```

### 2. Restart Individual Services

```yaml
# Restart specific services
- include_tasks: roles/01-common/tasks/task-restart-services.yml
  vars:
    restart_services:
      - "nginx"
      - "php-fpm-74"

# Restart a single service
- include_tasks: roles/01-common/tasks/task-restart-services.yml
  vars:
    restart_services:
      - "async-merge"
```

### 3. Mixed Restart (Groups + Individual Services)

```yaml
# Restart both groups and individual services
- include_tasks: roles/01-common/tasks/task-restart-services.yml
  vars:
    restart_groups:
      - "qps"
    restart_services:
      - "nginx"
      - "bureau-queue"
```

### 4. Playbook Integration Example

```yaml
---
- name: Medical Backend Service Restart Playbook
  hosts: all
  gather_facts: yes
  vars_files:
    - extra-vars/services.restart.yml
  
  tasks:
    - name: Restart identification services
      include_tasks: roles/01-common/tasks/task-restart-services.yml
      vars:
        restart_groups:
          - "recognize"
      when: restart_identification | default(false) | bool

    - name: Restart API services
      include_tasks: roles/01-common/tasks/task-restart-services.yml
      vars:
        restart_groups:
          - "api"
      when: restart_api | default(false) | bool
    
    - name: Restart specific services
      include_tasks: roles/01-common/tasks/task-restart-services.yml
      vars:
        restart_services: "{{ custom_services | default([]) }}"
      when: custom_services is defined and custom_services | length > 0
```

## Available Groups and Services

### Groups (发布端)
- **recognize** (识别端): async-merge, async-dispatcher, med-queue, async-recognize
- **qps** (QPS): selector
- **api** (API集群): php-fpm-74, nginx, med-queue
- **bureau** (事务局端): bureau-queue, med-cover-original, prescription
- **smart** (Smart药局): smart-queue

### Individual Services
- async-merge, async-dispatcher, med-queue, async-recognize
- selector, php-fpm-74, nginx, bureau-queue
- med-cover-original, prescription, smart-queue

## Features

### Robustness
- Pre-restart service status checking
- Docker Compose file existence validation
- Retry mechanism with configurable attempts and delays
- Timeout protection for restart operations
- Post-restart status verification

### Idempotency
- Safe to run multiple times
- Consistent results regardless of current service state
- Proper error handling and recovery

### Logging
- Comprehensive execution logging
- Pre and post-restart status recording
- Detailed error reporting
- Automatic log file cleanup (keeps last 20 executions)

### Error Handling
- Graceful handling of missing services
- Detailed error reporting with exit codes
- Partial failure support (continues with other services)
- Clear failure summaries

## Log Files Location
Default: `/tmp/ansible-service-restart-logs/`

### Log File Types
- `service-restart-execution.log`: Main execution log
- `service-status-{service}-{timestamp}.log`: Service status logs
- `service-restart-{service}-{timestamp}.log`: Individual restart logs

## Configuration Customization

You can override default settings by modifying `services.restart.yml`:

```yaml
services_info:
  restart:
    defaults:
      remote_logs_dir: "/custom/log/path"
      execution_timeout: -1  # -1 for no timeout, or specify seconds for timeout
      max_retry_attempts: 5
      retry_delay: 15  # 15 seconds
```

## Command Line Usage

```bash
# Restart by group
ansible-playbook site.yml -e "restart_groups=['recognize']"

# Restart individual services
ansible-playbook site.yml -e "restart_services=['nginx','php-fpm-74']"

# Mixed restart
ansible-playbook site.yml -e "restart_groups=['api'] restart_services=['selector']"
```
